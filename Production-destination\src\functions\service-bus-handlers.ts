/**
 * Service Bus Handlers for Azure Functions
 * Handles Service Bus queue and topic message processing
 */

import { InvocationContext, app } from '@azure/functions';
import {
  ServiceBusClient,
  ServiceBusMessage,
  ServiceBusSender,
  ServiceBusReceiver,
  DeadLetterOptions
} from '@azure/service-bus';
import { logger } from '../shared/utils/logger';
import { db } from '../shared/services/database';
import { publishEvent, EventType } from './event-grid-handlers';
import { redis } from '../shared/services/redis';

// Service Bus client and enhanced features
let serviceBusClient: ServiceBusClient | null = null;
let senders: Map<string, ServiceBusSender> = new Map();
let receivers: Map<string, ServiceBusReceiver> = new Map();

// Enhanced Service Bus metrics
interface ServiceBusMetrics {
  messagesSent: number;
  messagesReceived: number;
  messagesDeadLettered: number;
  errors: number;
  averageProcessingTime: number;
  activeConnections: number;
}

let metrics: ServiceBusMetrics = {
  messagesSent: 0,
  messagesReceived: 0,
  messagesDeadLettered: 0,
  errors: 0,
  averageProcessingTime: 0,
  activeConnections: 0
};

// Circuit breaker state
interface CircuitBreakerState {
  isOpen: boolean;
  failureCount: number;
  lastFailureTime: Date;
  threshold: number;
  timeout: number;
}

let circuitBreakers: Map<string, CircuitBreakerState> = new Map();

/**
 * Initialize Service Bus client
 */
function getServiceBusClient(): ServiceBusClient {
  if (!serviceBusClient) {
    const connectionString = process.env.SERVICE_BUS_CONNECTION_STRING;
    if (!connectionString) {
      throw new Error('Service Bus connection string not configured');
    }
    serviceBusClient = new ServiceBusClient(connectionString);
  }
  return serviceBusClient;
}

/**
 * Workflow orchestration queue handler
 * Handles workflow step execution messages
 */
async function workflowOrchestrationHandler(message: unknown, context: InvocationContext): Promise<void> {
  logger.info('Workflow orchestration handler triggered', {
    message,
    messageId: context.triggerMetadata?.messageId
  });

  try {
    const workflowMessage = typeof message === 'string' ? JSON.parse(message) : message;
    const {
      workflowId,
      stepId,
      action,
      data = {},
      userId,
      priority = 'normal'
    } = workflowMessage as any;

    if (!workflowId || !stepId || !action) {
      throw new Error('Invalid workflow message: missing required fields');
    }

    // Get workflow from database
    const workflows = await db.queryItems<any>('workflows',
      'SELECT * FROM c WHERE c.id = @workflowId',
      [workflowId]
    );

    if (workflows.length === 0) {
      throw new Error(`Workflow not found: ${workflowId}`);
    }

    const workflow = workflows[0];

    // Process workflow step
    const stepResult = await processWorkflowStep(workflow, stepId, action, data, userId);

    // Update workflow status
    await db.updateItem('workflows', {
      ...workflow,
      currentStep: stepResult.nextStep,
      status: stepResult.workflowStatus,
      lastExecutedAt: new Date().toISOString(),
      steps: workflow.steps.map((step: any) =>
        step.id === stepId
          ? { ...step, status: stepResult.stepStatus, executedAt: new Date().toISOString(), result: stepResult.result }
          : step
      )
    });

    // Publish workflow events
    if (stepResult.workflowStatus === 'completed') {
      await publishEvent(
        EventType.WORKFLOW_COMPLETED,
        `workflows/${workflowId}`,
        {
          workflowId,
          completedBy: userId,
          duration: Date.now() - new Date(workflow.createdAt).getTime(),
          timestamp: new Date().toISOString()
        }
      );
    }

    // Queue next step if workflow continues
    if (stepResult.nextStep && stepResult.workflowStatus === 'active') {
      await queueNextWorkflowStep(workflowId, stepResult.nextStep, userId);
    }

    logger.info('Workflow step processed successfully', {
      workflowId,
      stepId,
      action,
      nextStep: stepResult.nextStep,
      workflowStatus: stepResult.workflowStatus
    });

  } catch (error) {
    logger.error('Workflow orchestration handler failed', {
      message,
      error: error instanceof Error ? error.message : String(error)
    });

    // Handle workflow failure
    if (typeof message === 'object' && message !== null && 'workflowId' in message) {
      const workflowId = (message as any).workflowId;
      try {
        const workflows = await db.queryItems<any>('workflows',
          'SELECT * FROM c WHERE c.id = @workflowId',
          [workflowId]
        );

        if (workflows.length > 0) {
          const workflow = workflows[0];
          await db.updateItem('workflows', {
            ...workflow,
            status: 'failed',
            error: error instanceof Error ? error.message : String(error),
            failedAt: new Date().toISOString()
          });
        }
      } catch (updateError) {
        logger.error('Failed to update workflow status after failure', { updateError });
      }
    }
  }
}

/**
 * Document collaboration handler
 * Handles real-time document collaboration messages
 */
async function documentCollaborationHandler(message: unknown, context: InvocationContext): Promise<void> {
  logger.info('Document collaboration handler triggered', {
    message,
    messageId: context.triggerMetadata?.messageId
  });

  try {
    const collaborationMessage = typeof message === 'string' ? JSON.parse(message) : message;
    const {
      documentId,
      userId,
      action,
      data = {},
      sessionId,
      timestamp
    } = collaborationMessage as any;

    if (!documentId || !userId || !action) {
      throw new Error('Invalid collaboration message: missing required fields');
    }

    // Process collaboration action
    let result;
    switch (action) {
      case 'join':
        result = await handleUserJoinDocument(documentId, userId, sessionId);
        break;
      case 'leave':
        result = await handleUserLeaveDocument(documentId, userId, sessionId);
        break;
      case 'edit':
        result = await handleDocumentEdit(documentId, userId, data);
        break;
      case 'comment':
        result = await handleDocumentComment(documentId, userId, data);
        break;
      case 'share':
        result = await handleDocumentShare(documentId, userId, data);
        break;
      default:
        throw new Error(`Unknown collaboration action: ${action}`);
    }

    // Log collaboration activity
    await db.createItem('collaboration-logs', {
      id: `collab-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      documentId,
      userId,
      action,
      data,
      sessionId,
      result,
      timestamp: new Date().toISOString()
    });

    // Broadcast to other collaborators via SignalR
    await broadcastCollaborationUpdate(documentId, {
      action,
      userId,
      data,
      result,
      timestamp: new Date().toISOString()
    });

    logger.info('Document collaboration processed successfully', {
      documentId,
      userId,
      action,
      sessionId
    });

  } catch (error) {
    logger.error('Document collaboration handler failed', {
      message,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Analytics aggregation handler
 * Handles analytics data aggregation messages
 */
async function analyticsAggregationHandler(message: unknown, context: InvocationContext): Promise<void> {
  logger.info('Analytics aggregation handler triggered', {
    message,
    messageId: context.triggerMetadata?.messageId
  });

  try {
    const analyticsMessage = typeof message === 'string' ? JSON.parse(message) : message;
    const {
      eventType,
      entityId,
      entityType,
      metrics = {},
      timestamp,
      userId
    } = analyticsMessage as any;

    if (!eventType || !entityId || !entityType) {
      throw new Error('Invalid analytics message: missing required fields');
    }

    // Aggregate metrics based on event type
    const aggregationResult = await aggregateMetrics(eventType, entityId, entityType, metrics, userId);

    // Update analytics data
    await updateAnalyticsData(entityType, entityId, aggregationResult);

    // Check for analytics thresholds and alerts
    await checkAnalyticsThresholds(entityType, entityId, aggregationResult);

    logger.info('Analytics aggregation processed successfully', {
      eventType,
      entityId,
      entityType,
      metricsCount: Object.keys(metrics).length
    });

  } catch (error) {
    logger.error('Analytics aggregation handler failed', {
      message,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * System monitoring handler
 * Handles system monitoring and alerting messages
 */
async function systemMonitoringHandler(message: unknown, context: InvocationContext): Promise<void> {
  logger.info('System monitoring handler triggered', {
    message,
    messageId: context.triggerMetadata?.messageId
  });

  try {
    const monitoringMessage = typeof message === 'string' ? JSON.parse(message) : message;
    const {
      source,
      metricType,
      value,
      threshold = {},
      severity = 'info',
      timestamp
    } = monitoringMessage as any;

    if (!source || !metricType || value === undefined) {
      throw new Error('Invalid monitoring message: missing required fields');
    }

    // Store monitoring data
    await db.createItem('monitoring-data', {
      id: `monitor-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      source,
      metricType,
      value,
      severity,
      timestamp: timestamp || new Date().toISOString()
    });

    // Check thresholds and generate alerts
    if (threshold.warning && value > threshold.warning) {
      await generateMonitoringAlert('warning', source, metricType, value, threshold.warning);
    }

    if (threshold.critical && value > threshold.critical) {
      await generateMonitoringAlert('critical', source, metricType, value, threshold.critical);
    }

    logger.info('System monitoring processed successfully', {
      source,
      metricType,
      value,
      severity
    });

  } catch (error) {
    logger.error('System monitoring handler failed', {
      message,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

// Helper functions (placeholder implementations)
async function processWorkflowStep(workflow: any, stepId: string, action: string, data: any, userId: string): Promise<any> {
  // Implement workflow step processing logic
  return {
    stepStatus: 'completed',
    workflowStatus: 'active',
    nextStep: 'step-2',
    result: { processed: true }
  };
}

async function queueNextWorkflowStep(workflowId: string, stepId: string, userId: string): Promise<void> {
  // Queue next workflow step
  const client = getServiceBusClient();
  const sender = client.createSender('workflow-orchestration');

  await sender.sendMessages({
    body: JSON.stringify({
      workflowId,
      stepId,
      action: 'execute',
      userId,
      timestamp: new Date().toISOString()
    })
  });

  await sender.close();
}

async function handleUserJoinDocument(documentId: string, userId: string, sessionId: string): Promise<any> {
  return { action: 'joined', activeUsers: 2 };
}

async function handleUserLeaveDocument(documentId: string, userId: string, sessionId: string): Promise<any> {
  return { action: 'left', activeUsers: 1 };
}

async function handleDocumentEdit(documentId: string, userId: string, data: any): Promise<any> {
  return { action: 'edited', changes: data.changes };
}

async function handleDocumentComment(documentId: string, userId: string, data: any): Promise<any> {
  return { action: 'commented', commentId: 'comment-123' };
}

async function handleDocumentShare(documentId: string, userId: string, data: any): Promise<any> {
  return { action: 'shared', shareId: 'share-123' };
}

async function broadcastCollaborationUpdate(documentId: string, update: any): Promise<void> {
  // Implement SignalR broadcast
}

async function aggregateMetrics(eventType: string, entityId: string, entityType: string, metrics: any, userId: string): Promise<any> {
  return { aggregated: true, count: 1 };
}

async function updateAnalyticsData(entityType: string, entityId: string, data: any): Promise<void> {
  // Update analytics data in database
}

async function checkAnalyticsThresholds(entityType: string, entityId: string, data: any): Promise<void> {
  // Check for threshold breaches
}

async function generateMonitoringAlert(severity: string, source: string, metricType: string, value: number, threshold: number): Promise<void> {
  await publishEvent(
    EventType.PERFORMANCE_ALERT,
    `monitoring/${source}`,
    {
      alertType: 'threshold_breach',
      severity,
      source,
      metricType,
      value,
      threshold,
      timestamp: new Date().toISOString()
    }
  );
}

// Register Service Bus triggers
app.serviceBusQueue('workflowOrchestration', {
  connection: 'SERVICE_BUS_CONNECTION_STRING',
  queueName: 'workflow-orchestration',
  handler: workflowOrchestrationHandler
});

app.serviceBusTopic('documentCollaboration', {
  connection: 'SERVICE_BUS_CONNECTION_STRING',
  topicName: 'document-collaboration',
  subscriptionName: 'collaboration-processor',
  handler: documentCollaborationHandler
});

app.serviceBusTopic('analyticsAggregation', {
  connection: 'SERVICE_BUS_CONNECTION_STRING',
  topicName: 'analytics-events',
  subscriptionName: 'analytics-aggregator',
  handler: analyticsAggregationHandler
});

app.serviceBusTopic('systemMonitoring', {
  connection: 'SERVICE_BUS_CONNECTION_STRING',
  topicName: 'monitoring-events',
  subscriptionName: 'system-monitor',
  handler: systemMonitoringHandler
});

/**
 * Enhanced Service Bus utilities
 */

/**
 * Send message with enhanced features
 */
export async function sendEnhancedMessage(
  destination: string,
  message: any,
  options: {
    messageId?: string;
    sessionId?: string;
    timeToLive?: number;
    scheduledEnqueueTime?: Date;
    correlationId?: string;
    isQueue?: boolean;
  } = {}
): Promise<boolean> {
  const startTime = Date.now();

  try {
    // Check circuit breaker
    if (isCircuitBreakerOpen(destination)) {
      logger.warn('Circuit breaker is open for destination', { destination });
      return false;
    }

    // Check for duplicate message
    if (options.messageId && await isDuplicateMessage(options.messageId)) {
      logger.info('Duplicate message detected, skipping', { messageId: options.messageId });
      return true;
    }

    const client = getServiceBusClient();
    const sender = await getSender(destination);

    const serviceBusMessage: ServiceBusMessage = {
      body: JSON.stringify(message),
      messageId: options.messageId || generateMessageId(),
      sessionId: options.sessionId,
      timeToLive: options.timeToLive,
      scheduledEnqueueTimeUtc: options.scheduledEnqueueTime,
      correlationId: options.correlationId,
      applicationProperties: {
        originalTimestamp: new Date().toISOString(),
        source: 'enhanced-service-bus'
      }
    };

    await sender.sendMessages(serviceBusMessage);

    // Mark message as sent to prevent duplicates
    if (options.messageId) {
      await markMessageAsSent(options.messageId);
    }

    metrics.messagesSent++;
    updateProcessingTime(Date.now() - startTime);
    resetCircuitBreaker(destination);

    logger.info('Enhanced message sent successfully', {
      destination,
      messageId: serviceBusMessage.messageId,
      isQueue: options.isQueue
    });

    await publishMetricsEvent('message_sent', {
      destination,
      messageId: serviceBusMessage.messageId
    });

    return true;
  } catch (error) {
    metrics.errors++;
    recordCircuitBreakerFailure(destination);

    logger.error('Error sending enhanced message', {
      destination,
      error: error instanceof Error ? error.message : String(error)
    });
    return false;
  }
}

/**
 * Send batch of messages
 */
export async function sendBatchMessages(
  destination: string,
  messages: any[],
  isQueue: boolean = true
): Promise<boolean> {
  try {
    const sender = await getSender(destination);
    const serviceBusMessages: ServiceBusMessage[] = messages.map(msg => ({
      body: JSON.stringify(msg.body || msg),
      messageId: msg.messageId || generateMessageId(),
      sessionId: msg.sessionId,
      timeToLive: msg.timeToLive,
      correlationId: msg.correlationId,
      applicationProperties: {
        ...msg.applicationProperties,
        batchId: generateMessageId(),
        originalTimestamp: new Date().toISOString()
      }
    }));

    await sender.sendMessages(serviceBusMessages);

    metrics.messagesSent += messages.length;

    logger.info('Batch messages sent successfully', {
      destination,
      messageCount: messages.length,
      isQueue
    });

    await publishMetricsEvent('batch_sent', {
      destination,
      messageCount: messages.length,
      isQueue
    });

    return true;
  } catch (error) {
    metrics.errors++;
    logger.error('Error sending batch messages', {
      destination,
      messageCount: messages.length,
      error: error instanceof Error ? error.message : String(error)
    });
    return false;
  }
}

/**
 * Process dead letter messages
 */
export async function processDeadLetterQueue(
  queueOrTopicName: string,
  subscriptionName?: string
): Promise<void> {
  const client = getServiceBusClient();

  try {
    const deadLetterPath = subscriptionName
      ? `${queueOrTopicName}/subscriptions/${subscriptionName}/$deadletterqueue`
      : `${queueOrTopicName}/$deadletterqueue`;

    const receiver = client.createReceiver(deadLetterPath);

    const messages = await receiver.receiveMessages(10, { maxWaitTimeInMs: 5000 });

    for (const message of messages) {
      try {
        // Log dead letter message details
        logger.warn('Processing dead letter message', {
          messageId: message.messageId,
          deadLetterReason: message.deadLetterReason,
          deadLetterErrorDescription: message.deadLetterErrorDescription,
          deliveryCount: message.deliveryCount
        });

        // Store dead letter message for analysis
        await storeDeadLetterMessage(message);

        // Complete the message to remove it from dead letter queue
        await receiver.completeMessage(message);

        metrics.messagesDeadLettered++;
      } catch (error) {
        logger.error('Error processing dead letter message', {
          messageId: message.messageId,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    await receiver.close();
  } catch (error) {
    logger.error('Error processing dead letter queue', {
      queueOrTopicName,
      subscriptionName,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Get service metrics
 */
export function getServiceBusMetrics(): ServiceBusMetrics {
  return { ...metrics };
}

/**
 * Helper functions for enhanced Service Bus features
 */

async function getSender(destination: string): Promise<ServiceBusSender> {
  if (!senders.has(destination)) {
    const client = getServiceBusClient();
    const sender = client.createSender(destination);
    senders.set(destination, sender);
    metrics.activeConnections++;
  }
  return senders.get(destination)!;
}

async function isDuplicateMessage(messageId: string): Promise<boolean> {
  const key = `servicebus:sent:${messageId}`;
  return await redis.exists(key);
}

async function markMessageAsSent(messageId: string): Promise<void> {
  const key = `servicebus:sent:${messageId}`;
  await redis.setex(key, 3600, 'sent'); // Keep for 1 hour
}

function generateMessageId(): string {
  return `msg-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
}

function isCircuitBreakerOpen(destination: string): boolean {
  const breaker = circuitBreakers.get(destination);
  if (!breaker) {
    return false;
  }

  if (breaker.isOpen) {
    const now = new Date();
    if (now.getTime() - breaker.lastFailureTime.getTime() > breaker.timeout) {
      // Reset circuit breaker after timeout
      breaker.isOpen = false;
      breaker.failureCount = 0;
      logger.info('Circuit breaker reset', { destination });
    }
  }

  return breaker.isOpen;
}

function recordCircuitBreakerFailure(destination: string): void {
  let breaker = circuitBreakers.get(destination);
  if (!breaker) {
    breaker = {
      isOpen: false,
      failureCount: 0,
      lastFailureTime: new Date(),
      threshold: 5,
      timeout: 60000 // 1 minute
    };
    circuitBreakers.set(destination, breaker);
  }

  breaker.failureCount++;
  breaker.lastFailureTime = new Date();

  if (breaker.failureCount >= breaker.threshold) {
    breaker.isOpen = true;
    logger.warn('Circuit breaker opened', {
      destination,
      failureCount: breaker.failureCount
    });
  }
}

function resetCircuitBreaker(destination: string): void {
  const breaker = circuitBreakers.get(destination);
  if (breaker) {
    breaker.failureCount = 0;
    breaker.isOpen = false;
  }
}

function updateProcessingTime(processingTime: number): void {
  metrics.averageProcessingTime =
    (metrics.averageProcessingTime * 0.9) + (processingTime * 0.1);
}

async function storeDeadLetterMessage(message: any): Promise<void> {
  try {
    await db.createItem('dead-letter-messages', {
      id: `dl-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      messageId: message.messageId,
      body: message.body,
      deadLetterReason: message.deadLetterReason,
      deadLetterErrorDescription: message.deadLetterErrorDescription,
      deliveryCount: message.deliveryCount,
      enqueuedTimeUtc: message.enqueuedTimeUtc,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.debug('Failed to store dead letter message', {
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

async function publishMetricsEvent(eventType: string, data?: any): Promise<void> {
  try {
    await publishEvent(
      EventType.PERFORMANCE_ALERT,
      'servicebus/metrics',
      {
        service: 'servicebus',
        eventType,
        metrics,
        circuitBreakers: Array.from(circuitBreakers.entries()).map(([dest, state]) => ({
          destination: dest,
          isOpen: state.isOpen,
          failureCount: state.failureCount
        })),
        timestamp: new Date().toISOString(),
        ...data
      }
    );
  } catch (error) {
    // Don't log errors for metrics publishing to avoid recursion
  }
}

// Initialize periodic tasks
setInterval(async () => {
  // Process dead letter queues every 10 minutes
  const knownQueues = ['workflow-orchestration', 'ai-operations', 'scheduled-emails', 'document-processing', 'notification-delivery'];
  const knownTopics = [
    { topic: 'analytics-events', subscription: 'analytics-aggregator' },
    { topic: 'document-collaboration', subscription: 'collaboration-processor' },
    { topic: 'monitoring-events', subscription: 'system-monitor' }
  ];

  // Process queue dead letter queues
  for (const queue of knownQueues) {
    await processDeadLetterQueue(queue);
  }

  // Process topic dead letter queues
  for (const { topic, subscription } of knownTopics) {
    await processDeadLetterQueue(topic, subscription);
  }
}, 10 * 60 * 1000);

// Publish metrics every minute
setInterval(async () => {
  await publishMetricsEvent('periodic_metrics');
}, 60 * 1000);

// Export Service Bus utilities
export { getServiceBusClient };
