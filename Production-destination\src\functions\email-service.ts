/**
 * Email Service Function
 * Handles email sending and management
 * Migrated from old-arch/src/email-service/send/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { Client as PostmarkClient } from 'postmark';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { eventService } from '../shared/services/event';

// Email types and enums
enum EmailType {
  TRANSACTIONAL = 'TRANSACTIONAL',
  NOTIFICATION = 'NOTIFICATION',
  MARKETING = 'MARKETING',
  SYSTEM = 'SYSTEM',
  ALERT = 'ALERT'
}

enum EmailStatus {
  PENDING = 'PENDING',
  SENT = 'SENT',
  DELIVERED = 'DELIVERED',
  FAILED = 'FAILED',
  BOUNCED = 'BOUNCED',
  OPENED = 'OPENED',
  CLICKED = 'CLICKED'
}

enum EmailPriority {
  LOW = 'LOW',
  NORMAL = 'NORMAL',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

// Validation schemas
const sendEmailSchema = Joi.object({
  to: Joi.array().items(Joi.object({
    email: Joi.string().email().required(),
    name: Joi.string().max(100).optional()
  })).min(1).max(100).required(),
  cc: Joi.array().items(Joi.object({
    email: Joi.string().email().required(),
    name: Joi.string().max(100).optional()
  })).max(50).optional(),
  bcc: Joi.array().items(Joi.object({
    email: Joi.string().email().required(),
    name: Joi.string().max(100).optional()
  })).max(50).optional(),
  subject: Joi.string().min(1).max(200).required(),
  content: Joi.object({
    text: Joi.string().max(100000).optional(),
    html: Joi.string().max(100000).optional()
  }).or('text', 'html').required(),
  type: Joi.string().valid(...Object.values(EmailType)).default(EmailType.TRANSACTIONAL),
  priority: Joi.string().valid(...Object.values(EmailPriority)).default(EmailPriority.NORMAL),
  organizationId: Joi.string().uuid().required(),
  templateId: Joi.string().uuid().optional(),
  templateData: Joi.object().optional(),
  attachments: Joi.array().items(Joi.object({
    filename: Joi.string().required(),
    content: Joi.string().required(), // Base64 encoded
    contentType: Joi.string().required(),
    size: Joi.number().max(25 * 1024 * 1024).required() // 25MB max
  })).max(10).optional(),
  options: Joi.object({
    trackOpens: Joi.boolean().default(true),
    trackClicks: Joi.boolean().default(true),
    sendAt: Joi.string().isoDate().optional(),
    expiresAt: Joi.string().isoDate().optional(),
    replyTo: Joi.string().email().optional(),
    tags: Joi.array().items(Joi.string().max(50)).max(10).optional()
  }).optional()
});

const createTemplateSchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  description: Joi.string().max(500).optional(),
  subject: Joi.string().min(1).max(200).required(),
  content: Joi.object({
    text: Joi.string().max(100000).optional(),
    html: Joi.string().max(100000).optional()
  }).or('text', 'html').required(),
  type: Joi.string().valid(...Object.values(EmailType)).required(),
  organizationId: Joi.string().uuid().required(),
  variables: Joi.array().items(Joi.object({
    name: Joi.string().required(),
    type: Joi.string().valid('string', 'number', 'date', 'boolean').required(),
    required: Joi.boolean().default(false),
    defaultValue: Joi.any().optional(),
    description: Joi.string().optional()
  })).optional(),
  metadata: Joi.object().optional()
});

interface SendEmailRequest {
  to: Array<{ email: string; name?: string }>;
  cc?: Array<{ email: string; name?: string }>;
  bcc?: Array<{ email: string; name?: string }>;
  subject: string;
  content: {
    text?: string;
    html?: string;
  };
  type?: EmailType;
  priority?: EmailPriority;
  organizationId: string;
  templateId?: string;
  templateData?: any;
  attachments?: Array<{
    filename: string;
    content: string;
    contentType: string;
    size: number;
  }>;
  options?: {
    trackOpens?: boolean;
    trackClicks?: boolean;
    sendAt?: string;
    expiresAt?: string;
    replyTo?: string;
    tags?: string[];
  };
}

interface EmailMessage {
  id: string;
  to: Array<{ email: string; name?: string }>;
  cc?: Array<{ email: string; name?: string }>;
  bcc?: Array<{ email: string; name?: string }>;
  subject: string;
  content: {
    text?: string;
    html?: string;
  };
  type: EmailType;
  priority: EmailPriority;
  status: EmailStatus;
  organizationId: string;
  templateId?: string;
  templateData?: any;
  attachments: Array<{
    filename: string;
    contentType: string;
    size: number;
    storageUrl?: string;
  }>;
  options: {
    trackOpens: boolean;
    trackClicks: boolean;
    sendAt?: string;
    expiresAt?: string;
    replyTo?: string;
    tags: string[];
  };
  delivery: {
    sentAt?: string;
    deliveredAt?: string;
    openedAt?: string;
    clickedAt?: string;
    failedAt?: string;
    errorMessage?: string;
    attempts: number;
    maxAttempts: number;
  };
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  tenantId: string;
}

interface EmailTemplate {
  id: string;
  name: string;
  description?: string;
  subject: string;
  content: {
    text?: string;
    html?: string;
  };
  type: EmailType;
  organizationId: string;
  variables: Array<{
    name: string;
    type: string;
    required: boolean;
    defaultValue?: any;
    description?: string;
  }>;
  metadata: any;
  statistics: {
    timesUsed: number;
    lastUsed?: string;
    averageOpenRate: number;
    averageClickRate: number;
  };
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  tenantId: string;
}

/**
 * Send email handler
 */
export async function sendEmail(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Send email started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = sendEmailSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const emailRequest: SendEmailRequest = value;

    // Check organization access
    const hasAccess = await checkOrganizationAccess(emailRequest.organizationId, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Check email sending limits
    const canSend = await checkEmailLimits(emailRequest.organizationId, emailRequest.to.length);
    if (!canSend.allowed) {
      return addCorsHeaders({
        status: 429,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: canSend.reason }
      }, request);
    }

    // Process template if provided
    let finalContent = emailRequest.content;
    let finalSubject = emailRequest.subject;

    if (emailRequest.templateId) {
      const templateResult = await processEmailTemplate(emailRequest.templateId, emailRequest.templateData || {});
      if (templateResult.success) {
        finalContent = templateResult.content;
        finalSubject = templateResult.subject;
      }
    }

    // Store attachments if provided
    const processedAttachments = await processAttachments(emailRequest.attachments || []);

    // Create email message record
    const emailId = uuidv4();
    const now = new Date().toISOString();

    const emailMessage: EmailMessage = {
      id: emailId,
      to: emailRequest.to,
      cc: emailRequest.cc,
      bcc: emailRequest.bcc,
      subject: finalSubject,
      content: finalContent,
      type: emailRequest.type || EmailType.TRANSACTIONAL,
      priority: emailRequest.priority || EmailPriority.NORMAL,
      status: EmailStatus.PENDING,
      organizationId: emailRequest.organizationId,
      templateId: emailRequest.templateId,
      templateData: emailRequest.templateData,
      attachments: processedAttachments,
      options: {
        trackOpens: true,
        trackClicks: true,
        tags: [],
        ...emailRequest.options
      },
      delivery: {
        attempts: 0,
        maxAttempts: 3
      },
      createdBy: user.id,
      createdAt: now,
      updatedAt: now,
      tenantId: user.tenantId || user.id
    };

    await db.createItem('email-messages', emailMessage);

    // Send email immediately or schedule for later
    if (emailRequest.options?.sendAt && new Date(emailRequest.options.sendAt) > new Date()) {
      await scheduleEmail(emailMessage);
    } else {
      await deliverEmail(emailMessage);
    }

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "email_sent",
      userId: user.id,
      organizationId: emailRequest.organizationId,
      timestamp: now,
      details: {
        emailId,
        subject: finalSubject,
        recipientCount: emailRequest.to.length,
        emailType: emailRequest.type,
        priority: emailRequest.priority,
        hasAttachments: processedAttachments.length > 0,
        isScheduled: !!emailRequest.options?.sendAt
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'EmailSent',
      aggregateId: emailId,
      aggregateType: 'Email',
      version: 1,
      data: {
        email: {
          ...emailMessage,
          content: undefined, // Don't include content in event for privacy
          attachments: emailMessage.attachments.map(a => ({ filename: a.filename, size: a.size }))
        },
        sentBy: user.id
      },
      userId: user.id,
      organizationId: emailRequest.organizationId,
      tenantId: user.tenantId
    });

    logger.info("Email sent successfully", {
      correlationId,
      emailId,
      subject: finalSubject,
      recipientCount: emailRequest.to.length,
      emailType: emailRequest.type,
      sentBy: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        emailId,
        subject: finalSubject,
        recipientCount: emailRequest.to.length,
        status: EmailStatus.PENDING,
        type: emailRequest.type,
        priority: emailRequest.priority,
        scheduledFor: emailRequest.options?.sendAt,
        createdAt: now,
        message: emailRequest.options?.sendAt ? "Email scheduled successfully" : "Email sent successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Send email failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Create email template handler
 */
export async function createEmailTemplate(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create email template started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = createTemplateSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const templateRequest = value;

    // Check organization access
    const hasAccess = await checkOrganizationAccess(templateRequest.organizationId, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Check if template name already exists
    const existingTemplate = await checkTemplateExists(templateRequest.name, templateRequest.organizationId);
    if (existingTemplate) {
      return addCorsHeaders({
        status: 409,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Template with this name already exists" }
      }, request);
    }

    // Create email template
    const templateId = uuidv4();
    const now = new Date().toISOString();

    const template: EmailTemplate = {
      id: templateId,
      name: templateRequest.name,
      description: templateRequest.description,
      subject: templateRequest.subject,
      content: templateRequest.content,
      type: templateRequest.type,
      organizationId: templateRequest.organizationId,
      variables: templateRequest.variables || [],
      metadata: templateRequest.metadata || {},
      statistics: {
        timesUsed: 0,
        averageOpenRate: 0,
        averageClickRate: 0
      },
      createdBy: user.id,
      createdAt: now,
      updatedAt: now,
      tenantId: user.tenantId || user.id
    };

    await db.createItem('email-templates', template);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "email_template_created",
      userId: user.id,
      organizationId: templateRequest.organizationId,
      timestamp: now,
      details: {
        templateId,
        templateName: templateRequest.name,
        templateType: templateRequest.type,
        variableCount: templateRequest.variables?.length || 0
      },
      tenantId: user.tenantId
    });

    logger.info("Email template created successfully", {
      correlationId,
      templateId,
      templateName: templateRequest.name,
      templateType: templateRequest.type,
      createdBy: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: templateId,
        name: templateRequest.name,
        type: templateRequest.type,
        organizationId: templateRequest.organizationId,
        variableCount: templateRequest.variables?.length || 0,
        createdAt: now,
        message: "Email template created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create email template failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkOrganizationAccess(organizationId: string, userId: string): Promise<boolean> {
  try {
    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
    return memberships.length > 0;
  } catch (error) {
    logger.error('Failed to check organization access', { error, organizationId, userId });
    return false;
  }
}

async function checkEmailLimits(organizationId: string, recipientCount: number): Promise<{ allowed: boolean; reason?: string }> {
  try {
    // Get organization to check tier
    const organization = await db.readItem('organizations', organizationId, organizationId);
    if (!organization) {
      return { allowed: false, reason: 'Organization not found' };
    }

    const orgData = organization as any;
    const tier = orgData.tier || 'FREE';

    // Define tier limits
    const limits: { [key: string]: { dailyEmails: number; monthlyEmails: number } } = {
      'FREE': { dailyEmails: 100, monthlyEmails: 1000 },
      'PROFESSIONAL': { dailyEmails: 5000, monthlyEmails: 50000 },
      'ENTERPRISE': { dailyEmails: -1, monthlyEmails: -1 } // Unlimited
    };

    const limit = limits[tier] || limits['FREE'];

    if (limit.dailyEmails === -1) {
      return { allowed: true };
    }

    // Check daily limit (simplified)
    const today = new Date().toISOString().split('T')[0];
    const dailyCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.createdAt >= @today';
    const dailyCountResult = await db.queryItems('email-messages', dailyCountQuery, [organizationId, today]);
    const dailyCount = Number(dailyCountResult[0]) || 0;

    if (dailyCount + recipientCount > limit.dailyEmails) {
      return {
        allowed: false,
        reason: `Daily email limit exceeded (${limit.dailyEmails})`
      };
    }

    return { allowed: true };

  } catch (error) {
    logger.error('Failed to check email limits', { error, organizationId });
    return { allowed: false, reason: 'Failed to check limits' };
  }
}

async function checkTemplateExists(name: string, organizationId: string): Promise<boolean> {
  try {
    const existingQuery = 'SELECT * FROM c WHERE c.name = @name AND c.organizationId = @orgId';
    const existing = await db.queryItems('email-templates', existingQuery, [name, organizationId]);
    return existing.length > 0;
  } catch (error) {
    logger.error('Failed to check template exists', { error, name, organizationId });
    return false;
  }
}

async function processEmailTemplate(templateId: string, templateData: any): Promise<any> {
  try {
    const template = await db.readItem('email-templates', templateId, templateId);
    if (!template) {
      return { success: false, error: 'Template not found' };
    }

    const templateObj = template as any;

    // Simple template variable replacement
    let processedSubject = templateObj.subject;
    let processedText = templateObj.content.text || '';
    let processedHtml = templateObj.content.html || '';

    // Replace variables in format {{variableName}}
    for (const [key, value] of Object.entries(templateData)) {
      const placeholder = `{{${key}}}`;
      processedSubject = processedSubject.replace(new RegExp(placeholder, 'g'), String(value));
      processedText = processedText.replace(new RegExp(placeholder, 'g'), String(value));
      processedHtml = processedHtml.replace(new RegExp(placeholder, 'g'), String(value));
    }

    return {
      success: true,
      subject: processedSubject,
      content: {
        text: processedText || undefined,
        html: processedHtml || undefined
      }
    };

  } catch (error) {
    logger.error('Failed to process email template', { error, templateId });
    return { success: false, error: 'Template processing failed' };
  }
}

async function processAttachments(attachments: any[]): Promise<any[]> {
  const { BlobServiceClient } = require('@azure/storage-blob');

  try {
    if (!attachments || attachments.length === 0) {
      return [];
    }

    const connectionString = process.env.AZURE_STORAGE_CONNECTION_STRING;
    if (!connectionString) {
      logger.error('Azure Storage connection string not configured');
      throw new Error('Storage service not available');
    }

    const blobServiceClient = BlobServiceClient.fromConnectionString(connectionString);
    const containerName = 'email-attachments';
    const containerClient = blobServiceClient.getContainerClient(containerName);

    // Ensure container exists
    await containerClient.createIfNotExists({
      access: 'private'
    });

    const processedAttachments = [];

    for (const attachment of attachments) {
      try {
        // Generate unique blob name
        const blobName = `${uuidv4()}/${attachment.filename}`;
        const blockBlobClient = containerClient.getBlockBlobClient(blobName);

        // Upload attachment to blob storage
        const uploadOptions = {
          blobHTTPHeaders: {
            blobContentType: attachment.contentType || 'application/octet-stream'
          },
          metadata: {
            originalFilename: attachment.filename,
            uploadedAt: new Date().toISOString(),
            size: attachment.size?.toString() || '0'
          }
        };

        let uploadData: Buffer;
        if (attachment.content) {
          // Handle base64 encoded content
          uploadData = Buffer.isBuffer(attachment.content)
            ? attachment.content
            : Buffer.from(attachment.content, 'base64');
        } else {
          throw new Error('Attachment content is required');
        }

        await blockBlobClient.upload(uploadData, uploadData.length, uploadOptions);

        // Generate SAS URL for secure access (valid for 7 days)
        const sasUrl = await blockBlobClient.generateSasUrl({
          permissions: 'r',
          expiresOn: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
        });

        processedAttachments.push({
          filename: attachment.filename,
          contentType: attachment.contentType,
          size: attachment.size,
          blobName: blobName,
          storageUrl: sasUrl,
          uploadedAt: new Date().toISOString()
        });

        logger.info('Attachment uploaded to blob storage', {
          filename: attachment.filename,
          blobName: blobName,
          size: attachment.size
        });

      } catch (attachmentError) {
        logger.error('Failed to process individual attachment', {
          filename: attachment.filename,
          error: attachmentError instanceof Error ? attachmentError.message : String(attachmentError)
        });
        // Continue processing other attachments
      }
    }

    return processedAttachments;
  } catch (error) {
    logger.error('Failed to process attachments', {
      error: error instanceof Error ? error.message : String(error)
    });
    return [];
  }
}

async function scheduleEmail(emailMessage: EmailMessage): Promise<void> {
  const { ServiceBusClient } = require('@azure/service-bus');

  try {
    const connectionString = process.env.AZURE_SERVICE_BUS_CONNECTION_STRING;
    if (!connectionString) {
      logger.error('Service Bus connection string not configured');
      throw new Error('Email scheduling service not available');
    }

    const serviceBusClient = new ServiceBusClient(connectionString);
    const queueName = 'scheduled-emails';
    const sender = serviceBusClient.createSender(queueName);

    // Calculate delay for scheduled delivery
    const sendAt = emailMessage.options.sendAt ? new Date(emailMessage.options.sendAt) : new Date();
    const delay = Math.max(0, sendAt.getTime() - Date.now());

    const message = {
      body: {
        emailId: emailMessage.id,
        type: 'scheduled-email-delivery',
        emailData: emailMessage
      },
      messageId: `email-${emailMessage.id}-${Date.now()}`,
      scheduledEnqueueTime: sendAt,
      applicationProperties: {
        emailId: emailMessage.id,
        organizationId: emailMessage.organizationId,
        priority: emailMessage.priority,
        type: 'email-delivery'
      }
    };

    await sender.sendMessages(message);
    await sender.close();
    await serviceBusClient.close();

    logger.info('Email scheduled for delivery via Service Bus', {
      emailId: emailMessage.id,
      scheduledFor: sendAt.toISOString(),
      delayMs: delay,
      queueName: queueName
    });

  } catch (error) {
    logger.error('Failed to schedule email', {
      emailId: emailMessage.id,
      error: error instanceof Error ? error.message : String(error)
    });

    // Fallback: immediate delivery if scheduling fails
    logger.info('Falling back to immediate email delivery');
    await deliverEmail(emailMessage);
  }
}

async function deliverEmail(emailMessage: EmailMessage): Promise<void> {
  try {
    // Update status to sent
    const updatedMessage = {
      ...emailMessage,
      id: emailMessage.id,
      status: EmailStatus.SENT,
      delivery: {
        ...emailMessage.delivery,
        sentAt: new Date().toISOString(),
        attempts: emailMessage.delivery.attempts + 1
      },
      updatedAt: new Date().toISOString()
    };

    await db.updateItem('email-messages', updatedMessage);

    // Production email delivery using Postmark
    const postmarkToken = process.env.POSTMARK_SERVER_TOKEN;
    const fromEmail = process.env.POSTMARK_FROM_EMAIL || '<EMAIL>';

    if (!postmarkToken) {
      throw new Error('Postmark server token not configured');
    }

    const postmarkClient = new PostmarkClient(postmarkToken);

    // Prepare email data for Postmark
    const emailData = {
      From: fromEmail,
      To: emailMessage.to.join(','),
      Cc: emailMessage.cc?.join(','),
      Bcc: emailMessage.bcc?.join(','),
      Subject: emailMessage.subject,
      HtmlBody: emailMessage.htmlBody,
      TextBody: emailMessage.textBody,
      ReplyTo: emailMessage.replyTo,
      Headers: emailMessage.headers ? Object.entries(emailMessage.headers).map(([Name, Value]) => ({ Name, Value })) : undefined,
      Tag: emailMessage.type,
      TrackOpens: true,
      TrackLinks: 'HtmlAndText',
      MessageStream: 'outbound'
    };

    // Send email via Postmark
    const result = await postmarkClient.sendEmail(emailData);

    logger.info('Email delivered successfully via Postmark', {
      emailId: emailMessage.id,
      postmarkMessageId: result.MessageID,
      recipientCount: emailMessage.to.length,
      submittedAt: result.SubmittedAt
    });

    // Update with external message ID
    const finalMessage = {
      ...updatedMessage,
      externalMessageId: result.MessageID,
      updatedAt: new Date().toISOString()
    };

    await db.updateItem('email-messages', finalMessage);

  } catch (error) {
    logger.error('Failed to deliver email', { error, emailId: emailMessage.id });

    // Update status to failed
    const failedMessage = {
      ...emailMessage,
      id: emailMessage.id,
      status: EmailStatus.FAILED,
      delivery: {
        ...emailMessage.delivery,
        failedAt: new Date().toISOString(),
        errorMessage: error instanceof Error ? error.message : 'Delivery failed',
        attempts: emailMessage.delivery.attempts + 1
      },
      updatedAt: new Date().toISOString()
    };

    await db.updateItem('email-messages', failedMessage);
  }
}

// Register functions
app.http('email-send', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'emails/send',
  handler: sendEmail
});

app.http('email-template-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'emails/templates',
  handler: createEmailTemplate
});
